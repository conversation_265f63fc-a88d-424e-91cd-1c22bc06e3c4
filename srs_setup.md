# SRS服务器快速设置

## 1. 下载SRS
```bash
# Windows
wget https://github.com/ossrs/srs/releases/download/v5.0-r4/SRS-Windows-x86_64-5.0-r4.zip

# 或者使用Docker
docker run --rm -it -p 1935:1935 -p 8080:8080 ossrs/srs:5
```

## 2. 配置文件 (srs.conf)
```conf
listen              1935;
max_connections     1000;
srs_log_tank        file;
srs_log_file        ./objs/srs.log;

http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}

http_api {
    enabled         on;
    listen          1985;
}

stats {
    network         0;
}

rtc_server {
    enabled on;
    listen 8000;
    candidate $CANDIDATE;
}

vhost __defaultVhost__ {
    hls {
        enabled         on;
        hls_path        ./objs/nginx/html;
        hls_fragment    2;
        hls_window      60;
    }
    
    http_remux {
        enabled     on;
        mount       [vhost]/[app]/[stream].flv;
    }
    
    rtc {
        enabled     on;
        rtmp_to_rtc on;
        rtc_to_rtmp on;
    }
}
```

## 3. 启动SRS
```bash
./objs/srs -c srs.conf
```

## 4. 推流到SRS
```bash
# 你的RTMP流会自动转换为多种格式
# RTMP输入: rtmp://127.0.0.1:1935/live/stream
# HLS输出: http://127.0.0.1:8080/live/stream.m3u8
# FLV输出: http://127.0.0.1:8080/live/stream.flv
```
