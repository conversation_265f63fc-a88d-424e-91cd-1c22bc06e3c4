setTimeout(function() {
	//解决图片跨域问题
	const host = window.location.host
	var map_center = [121.536073,38.979226]
	// 随机颜色
	var color_list = ['#91d2f2','#add094','#f1aaba','#a9d5c6','#f0ca83','#d5c0cd']

	window._AMapSecurityConfig = {
	  securityJsCode: "3f4de913d36220fefe704d87c6b9fdeb",
	};
	AMapLoader.load({
		key: "261bc0c4c8207f2c6c040abf9ada6c87", //申请好的 Web 端开发 Key，首次调用 load 时必填
		version: "2.0", //指定要加载的 JS API 的版本，缺省时默认为 1.4.15
		plugins: ["AMap.Scale","AMap.ToolBar","AMap.InfoWindow","AMap.PlaceSearch","AMap.AutoComplete",
			"AMap.Driving","AMap.Adaptor","AMap.HeatMap","AMap.MarkerCluster","AMap.MouseTool",
			"AMap.IndexCluster","AMap.CircleEditor"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['AMap.Scale','...','...']
		AMapUI: {
			//是否加载 AMapUI，缺省不加载
			version: "1.1", //AMapUI 版本
			plugins: ["overlay/SimpleMarker"], //需要加载的 AMapUI ui 插件
		},
		Loca: {
			//是否加载 Loca， 缺省不加载
			version: "2.0", //Loca 版本
		},
	})
	.then((AMap) => {
	var map = new AMap.Map("container",{
		resizeEnable: true,
		viewMode: '2D', //默认使用 2D 模式
		zoom: 10, //地图级别
		center: map_center, //地图中心点
		isHotspot: true,
	}); //"container"为 <div> 容器的 id
	map.addControl(new AMap.Scale()); //添加比例尺组件到地图实例上



	//图片信息卡片
	var imgCard = new AMap.InfoWindow({
		isCustom: true,  //使用自定义窗体
		autoMove: false,
		// content: createInfoWindow(title, content.join("<br/>")),
		offset: new AMap.Pixel(28, -10)
	  });
	  //关闭信息窗体
	function closeInfoWindow() {
		map.clearInfoWindow();
	}
	 //构建自定义信息窗体
	function createInfoCard(title, content, type,code=null) {
		//主体
		var bodyD = document.createElement("div");
		bodyD.className = "card border-primary mb-3";
		bodyD.style.maxWidth = "480px";
		bodyD.style.minWidth = "240px";
		// 栅栏格式
		var layoutD = document.createElement("div");
		layoutD.className = "row g-0";
		//图片位置
		var imgLayoutD = document.createElement("div");
		imgLayoutD.className = "col-md-5 align-self-center";
		//图片
		var imgD = document.createElement("img");
		imgD.className = "img-fluid rounded-start";
		imgD.style.width = "100%";
		imgD.src = "http://"+host+"/webroot/static/map/" + content[0];
		// imgLayoutD.appendChild(imgD);

		//信息位置
		var infoLayoutD = document.createElement("div");
		infoLayoutD.className = "col-md-12 align-self-start";
		//信息位置
		var infoBodyLayoutD = document.createElement("div");
		infoBodyLayoutD.className = "card-body";
		//信息头部
		var infoBodyHeaderLayoutD = document.createElement("div");
		infoBodyHeaderLayoutD.className = "offcanvas-header";
		//标题
		var titleD = document.createElement("h5");
		titleD.className = "offcanvas-title";
		titleD.innerHTML = title;
		//关闭按钮
		var infoBodyCloseBtn = document.createElement("button");
		infoBodyCloseBtn.className = "btn-close";
		infoBodyCloseBtn.type = "button";

		var labelAttribute = document.createAttribute('aria-label');
		labelAttribute.value = 'Close';
		infoBodyCloseBtn.setAttributeNode(labelAttribute);
		//关闭信息窗体
		infoBodyCloseBtn.onclick = closeInfoWindow;

		//添加至头部
		infoBodyHeaderLayoutD.appendChild(titleD);
		infoBodyHeaderLayoutD.appendChild(infoBodyCloseBtn);

		//文字
		var contentD = document.createElement("p");
		contentD.className = "card-text mb-0";
		//数组切片
		contentD.innerHTML =  content.slice(0).join("<br/>");
		//小文字
		var contentSmallD = document.createElement("p");
		contentSmallD.className = "card-text mb-0";
		//小文字
		var smallD = document.createElement("small");
		smallD.className = "text-muted";
		smallD.innerHTML = content[0] + "<br/>" + content[2];

		// contentSmallD.appendChild(smallD);
		//a标签 视频标签
		var linkD = document.createElement("a");
        linkD.target = "_blank";
        linkD.className = "link-primary";

		//a标签 视频标签
		var linkD1 = document.createElement("a");
        linkD1.target = "_blank";
        linkD1.className = "link-primary ms-3";

		infoBodyLayoutD.appendChild(infoBodyHeaderLayoutD);
		infoBodyLayoutD.appendChild(contentSmallD);
		infoBodyLayoutD.appendChild(contentD);
		//根据不同点 添加a标签
		switch (type) {
			case 1:
				linkD.innerHTML = "营区监控";
				linkD.href = "http://"+host+"/webroot/decision/view/duchamp?viewlet=35%25E8%25A7%2586%25E9%25A2%2591%25E7%259B%2591%25E6%258E%25A7%252Fvideo.fvs&ref_t=design&ref_c=c58c054f-2ac4-4d45-97f4-d9e48e401cfd&page_number=1&jyz=" + title;
				infoBodyLayoutD.appendChild(linkD);
				break;
			case 2:
			case 15:
			case 16:
			case 17:
				linkD.innerHTML = "单位详情";
				linkD.href = "http://"+host+"/webroot/decision/view/duchamp?ref_c=228e000c-e31e-461b-928a-51dea331dd2b&viewlet=34%25E6%258C%2587%25E6%258C%25A5%25E8%25B0%2583%25E5%25BA%25A6%252F%25E5%258D%2595%25E4%25BD%258D%25E4%25BF%25A1%25E6%2581%25AF%252F%25E5%258D%2595%25E4%25BD%258D%25E4%25BF%25A1%25E6%2581%25AF.fvs&ref_t=design&page_number=1&id="+code+"&danwei="+title;
				infoBodyLayoutD.appendChild(linkD);

				// linkD1.innerHTML = "检查详情";
				// linkD1.id ="keyUnit";
				//  //linkD1.href = "http://"+host+"/webroot/decision/view/duchamp?ref_c=228e000c-e31e-461b-928a-51dea331dd2b&viewlet=34%25E6%258C%2587%25E6%258C%25A5%25E8%25B0%2583%25E5%25BA%25A6%252F%25E5%258D%2595%25E4%25BD%258D%25E4%25BF%25A1%25E6%2581%25AF%252F%25E5%258D%2595%25E4%25BD%258D%25E4%25BF%25A1%25E6%2581%25AF.fvs&ref_t=design&page_number=1&id="+code+"&danwei="+title;
				// infoBodyLayoutD.appendChild(linkD1);

				// 绑定点击事件
				// linkD1.addEventListener('click', function(event) {
				// 	window.parent.postMessage(code, 'http://'+host)
				// });
				break;
			default:
				break;
		}
		infoLayoutD.appendChild(infoBodyLayoutD);
		//添加至信息体
		layoutD.appendChild(imgLayoutD);
		layoutD.appendChild(infoLayoutD);

		bodyD.appendChild(layoutD);

		return bodyD;
	}
	//百度转高德
	function bd_decrypt(bd_lng, bd_lat) {
		var X_PI = Math.PI * 3000.0 / 180.0;
		var x = bd_lng - 0.0065;
		var y = bd_lat - 0.006;
		var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
		var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
		var gg_lng = z * Math.cos(theta);
		var gg_lat = z * Math.sin(theta);
		return [gg_lng,gg_lat]
	}
	//wgs84转高德
	function wgs84togcj02(lng, lat) {
		// 定义一些常量
		var PI = 3.1415926535897932384626;
		var a = 6378245.0;
		var ee = 0.00669342162296594323;

		var lat = +lat;
		var lng = +lng;

		// 判断是否在国内，不在国内则不做偏移
		if (!(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55)) {
		  return [lng, lat]
		}
		else {
		  // 转换lat
		  var dlatplng = lng - 105.0, dlatplat = lat - 35.0;
		  var dlat = -100.0 + 2.0 * dlatplng + 3.0 * dlatplat + 0.2 * dlatplat * dlatplat + 0.1 * dlatplng * dlatplat + 0.2 * Math.sqrt(Math.abs(dlatplng));
		  dlat += (20.0 * Math.sin(6.0 * dlatplng * PI) + 20.0 * Math.sin(2.0 * dlatplng * PI)) * 2.0 / 3.0;
		  dlat += (20.0 * Math.sin(dlatplat * PI) + 40.0 * Math.sin(dlatplat / 3.0 * PI)) * 2.0 / 3.0;
		  dlat += (160.0 * Math.sin(dlatplat / 12.0 * PI) + 320 * Math.sin(dlatplat * PI / 30.0)) * 2.0 / 3.0;

		  // 转换lng
		  var dlngplng = lng - 105.0, dlngplat = lat - 35.0;
		  var dlng = 300.0 + dlngplng + 2.0 * dlngplat + 0.1 * dlngplng * dlngplng + 0.1 * dlngplng * dlngplat + 0.1 * Math.sqrt(Math.abs(dlngplng));
		  dlng += (20.0 * Math.sin(6.0 * dlngplng * PI) + 20.0 * Math.sin(2.0 * dlngplng * PI)) * 2.0 / 3.0;
		  dlng += (20.0 * Math.sin(dlngplng * PI) + 40.0 * Math.sin(dlngplng / 3.0 * PI)) * 2.0 / 3.0;
		  dlng += (150.0 * Math.sin(dlngplng / 12.0 * PI) + 300.0 * Math.sin(dlngplng / 30.0 * PI)) * 2.0 / 3.0;

		  var radlat = lat / 180.0 * PI;
		  var magic = Math.sin(radlat);
		  magic = 1 - ee * magic * magic;
		  var sqrtmagic = Math.sqrt(magic);
		  dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);
		  dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);
		  var mglat = lat + dlat;
		  var mglng = lng + dlng;

		  return [mglng, mglat]
		}
	}

    //信息窗处理
    function cleanContent(info){
		// console.log(info)
        if (info == null) {
            return ['暂无信息']
		}
        else{
            return info.split(",")
        }

    }

	// 筛选过滤图标数据
	var marks_jyz = [];
	var marks_dadui = [];
	var marks_water0 = [];
	//水鹤
	var marks_water1 = [];
	//库房
	var marks_warehouse = [];
	// 微型消防站
	var marks_miniFireStation = [];
	//企业专职队
	var marks_professionalFire = [];
	//派出所
	var marks_psb = [];
	//派出所
	var marks_jiedao = [];
	//服务机构
	var marks_fireService = [];
	//铁塔
	var marks_tieta = [];
	//物联网
	var marks_iot = [];
	//民间救援组织
	var marks_civilianRescue = [];
	//民间救援组织
	var marks_serverUnit = [];
	//重点单位
	var marks_keyUnit = [];
	//重点单位风险评估
	var marks_keyUnit_assessment= [];
          //消防工作站
	var marks_workstation = [];


	//数据源
	var marker_info_str = "["+ marker_info.toString() + "]" ;
	// console.log(marker_info_str)
	var mark_list = JSON.parse(marker_info_str);
	// console.log(marker_info_str)
	for(let i = 0;i < mark_list.length; i++) {
		// console.log(mark_list[i]['style'])

		switch (mark_list[i]['style']) {
			case 0:
				marks_dadui.push(mark_list[i]);
				break;
			case 1:
				marks_jyz.push(mark_list[i]);
				break;
			case 2:
			case 15:
			case 16:
			case 17:
				if (mark_list[i]['content']) {
					marks_keyUnit.push(mark_list[i]);
				}
				else{
					marks_keyUnit_assessment.push(mark_list[i]);
				}
				break;
			case 3:
				marks_water0.push(mark_list[i]);
				break;
			case 4:
				marks_water1.push(mark_list[i]);
				break;
			case 5:
				//百度坐标点转换
				mark_list[i]['lnglat'] = bd_decrypt(mark_list[i]['lnglat'][0],mark_list[i]['lnglat'][1]);
				marks_warehouse.push(mark_list[i]);
				break;
			case 6:
				//百度坐标点转换 企业专职队
				mark_list[i]['lnglat'] = bd_decrypt(mark_list[i]['lnglat'][0],mark_list[i]['lnglat'][1]);
				marks_professionalFire.push(mark_list[i]);
				break;
			case 7:
				//百度坐标点转换 微型消防站
				mark_list[i]['lnglat'] = bd_decrypt(mark_list[i]['lnglat'][0],mark_list[i]['lnglat'][1]);
				marks_miniFireStation.push(mark_list[i]);
				break;
			case 8:
				//派出所
				marks_psb.push(mark_list[i]);
				break;
			case 9:
				//街道
				marks_jiedao.push(mark_list[i]);
				break;
			case 10:
			case 19:
			case 20:
				//服务机构
				marks_fireService.push(mark_list[i]);
				break;
			case 11:
				//铁塔
				marks_tieta.push(mark_list[i]);
				break;
			case 12:
				//物联网
				marks_iot.push(mark_list[i]);
				break;
			case 13:
				//民间救援组织
				marks_civilianRescue.push(mark_list[i]);
				break;
			case 14:
				//民间救援组织 下一个18
				marks_serverUnit.push(mark_list[i]);
				break;
			case 21:
				//重点单位风险评估
				marks_keyUnit_assessment.push(mark_list[i]);
				break;
                              case 22:
                              case 23:
                              case 24:
				//重点单位风险评估
				marks_workstation.push(mark_list[i]);
				break;
			default:
				//大队中心点
				var district = mark_list[i];
				// console.log(district);
				break;
		}
	}


	// 添加海量点.
	function addMassMarks(data) {

		// 创建海量点样式对象
		let styleObject = [{
			url: 'http://'+host+'/webroot/static/map/picture/dadui.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 大队
			size: new AMap.Size(21, 21),
			zIndex: 10,
			// 图标大小0
		},{
			url: 'http://'+host+'/webroot/static/map/picture/jyz.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 队站
			size: new AMap.Size(21, 21),
			zIndex: 11,
			// 图标大小1
		},{
			url:'http://'+host+'/webroot/static/map/picture/keyunit.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 重点单位
			size: new AMap.Size(21, 21),
			// 图标大小2
		},{
			url: 'http://'+host+'/webroot/static/map/picture/jyz.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 消防栓
			size: new AMap.Size(20, 20),
			// 图标大小3
		},{
			url:  'http://'+host+'/webroot/static/map/picture/xiaofangshuihe.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 消防水鹤
			size: new AMap.Size(21, 21),
			// 图标大小4
		},{
			url: 'http://'+host+'/webroot/static/map/picture/kufang.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 库房
			size: new AMap.Size(21, 21),
			// 图标大小5
		},{
			url: 'http://'+host+'/webroot/static/map/picture/qiyezhuanzhi.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 企业专职队
			size: new AMap.Size(21, 21),
			// 图标大小6
		},{
			url: 'http://'+host+'/webroot/static/map/picture/weixingxiaofang.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 微型消防站
			size: new AMap.Size(21, 21),
			// 图标大小7
		},{
			url: 'http://'+host+'/webroot/static/map/picture/psb.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 派出所
			size: new AMap.Size(21, 21),
			// 图标大小8
		},{
			url: 'http://'+host+'/webroot/static/map/picture/jiedao.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 街道
			size: new AMap.Size(21, 21),
			// 图标大小9
		},{
			url: 'http://'+host+'/webroot/static/map/picture/fireservice.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 街道
			size: new AMap.Size(21, 21),
			// 图标大小10
		},{
			url: 'http://'+host+'/webroot/static/map/picture/tieta.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 铁塔
			size: new AMap.Size(21, 21),
			// 图标大小11
		},{
			url: 'http://'+host+'/webroot/static/map/picture/iot.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 物联网
			size: new AMap.Size(21, 21),
			// 图标大小12
		},{
			url: 'http://'+host+'/webroot/static/map/picture/civrescue.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 民间
			size: new AMap.Size(21, 21),
			// 图标大小13
		},{
			url: 'http://'+host+'/webroot/static/map/picture/servicesunit.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 保障单位
			size: new AMap.Size(21, 21),
			// 图标大小14
		},{
			url:'http://'+host+'/webroot/static/map/picture/keyunit1.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 重点单位灰
			size: new AMap.Size(21, 21),
			// 图标大小15
		},{
			url:'http://'+host+'/webroot/static/map/picture/keyunit2.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 重点单位黄
			size: new AMap.Size(21, 21),
			// 图标大小16
		},{
			url:'http://'+host+'/webroot/static/map/picture/keyunit3.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 重点单位红
			size: new AMap.Size(21, 21),
			// 图标大小17
		},{
			url:'http://'+host+'/webroot/static/map/picture/fire_car.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 执勤车辆
			size: new AMap.Size(23, 23),
			// 图标大小18
		},{
			url:'http://'+host+'/webroot/static/map/picture/fireservice2.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 执勤车辆
			size: new AMap.Size(23, 23),
			// 图标大小19
		},{
			url:'http://'+host+'/webroot/static/map/picture/fireservice1.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 执勤车辆
			size: new AMap.Size(23, 23),
			// 图标大小20
		},{
			url:'http://'+host+'/webroot/static/map/picture/keyunit4.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 重点单位橙
			size: new AMap.Size(23, 23),
			// 图标大小21
		},{
			url:'http://'+host+'/webroot/static/map/picture/workstation1.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 消防工作站绿
			size: new AMap.Size(23, 23),
			// 图标大小22
		},{
			url:'http://'+host+'/webroot/static/map/picture/workstation2.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 消防工作站红
			size: new AMap.Size(23, 23),
			// 图标大小23
		},{
			url:'http://'+host+'/webroot/static/map/picture/workstation3.png', // 图标地址
			anchor: new AMap.Pixel(0, 0), // 图标显示位置偏移量，基准点为图标左上角 消防工作站灰
			size: new AMap.Size(23, 23),
			// 图标大小24
		}
		];
		var marks = new AMap.MassMarks(data, {
			cursor: 'pointer',
			zooms: [6, 22], // 在指定地图缩放级别范围内展示海量点图层
			style: styleObject, // 设置样式对象
			zIndex: 200, // 海量点图层叠加的顺序
		});
		var marker = new AMap.Marker({content: ' ', map: map});

		//救援站点击事件  右侧框
	// jyzMarks.on('click', function(e) {
	// 	// _g().getWidgetByName("info_window").setVisible(true);
	// 	var info_window = document.getElementById("offcanvasScrolling");
	// 	//清除历史信息
	// 	// while (info_window.firstChild) {
	// 	// 	info_window.removeChild(info_window.firstChild);
	// 	// }
	// 	//显示信息窗体
	// 	info_window.className = "offcanvas offcanvas-start show";
	// 	var titleD = document.getElementById("offcanvasScrollingLabel");
	// 	titleD.innerHTML = e.data.name;
	// 	var infoD = document.getElementById("offcanvasScrollingInfo");
	// 	infoD.innerHTML = e.data.lnglat;
	// });



		if ([0,1,2,15,16,17].indexOf(data[0]['style']) !== -1 && typeof data[0]['content'] !== 'undefined') {
			marks.on('click', function(e) {
				imgCard.setContent(createInfoCard(e.data.name, cleanContent(e.data.content),e.data.style,e.data.code))
				imgCard.open(map, e.data.lnglat);
				//FVS通信
				//window.parent.postMessage(e.data.name, 'http://data.dalian119.com:8075')

			});
		}else{
			//非大队救援站坐标点
			marks.on('mouseover', function(e){
				var position = e.data.name && e.data.lnglat;
				// console.log(position);
				// console.log(e.data);
				if(position){
					marker.setPosition(e.data.lnglat);
					marker.setLabel({content: e.data.name,offset:[22,10,0,0]})
					map.add(marker);
				}
			});
			marks.on('mouseout', function(){
				map.remove(marker);
			});
			//点击事件判断 铁塔弹出窗口
			if ([11,12].indexOf(data[0]['style']) !== -1) {
				marks.on('click', function(e) {
					switch (e.data.type) {
						case "救援站":
							url = "http://"+host+"/webroot/decision/view/duchamp?viewlet=35%25E8%25A7%2586%25E9%25A2%2591%25E7%259B%2591%25E6%258E%25A7%252Fvideo.fvs&ref_t=design&ref_c=c58c054f-2ac4-4d45-97f4-d9e48e401cfd&page_number=";
							url = url + "1" + "&jyz=" +e.data.name;
							break;
						case "铁塔":
							url = "http://"+host+"/webroot/decision/view/duchamp?viewlet=35%25E8%25A7%2586%25E9%25A2%2591%25E7%259B%2591%25E6%258E%25A7%252Ftieta.fvs&ref_t=design&ref_c=c58c054f-2ac4-4d45-97f4-d9e48e401cfd&page_number=";
							url = url + "1" + "&jyz=" +e.data.name;
							break;
						case "物联网":
							var token = _g().getWidgetByName('refresh_data').getCell(1,1).getValue();
							var zs_token = _g().getWidgetByName('refresh_data').getCell(1,2).getValue();
							//截取token
							var iot_token = token.split('value":"',2)[1].slice(0,-2);
							//中山物联网
							var zs_iot_token = JSON.parse(JSON.parse(zs_token)['value']);

							url = "https://gxxf-master.yundun119.com/#/login_by_token?orgId="+e.data.code+"&ritenToken="+iot_token;
							if (e.data.iotType === '2') {
								url = zs_iot_token[e.data.name1]
							}
							break;
						default:
							break;
					}
					window.open(url);
				});
			}
		}

		marks.setMap(map);
		return marks
	}

	var cluster;
	// 海量点聚合
	function addCluster(img_url,data) {
		var sts = [{
			url: img_url[0],
			size: new AMap.Size(56, 21),
			offset: new AMap.Pixel(-16, -16),
			imageOffset: new AMap.Pixel(-1, 0),
			textColor: "#fff",
			textSize:9
		},
		{
			url: img_url[0],
			size: new AMap.Size(56, 21),
			offset: new AMap.Pixel(-16, -16),
			imageOffset: new AMap.Pixel(-1, 0),
			textColor: "#fff",
			textSize:9
		}, {
			url: img_url[0],
			size: new AMap.Size(56, 21),
			offset: new AMap.Pixel(-16, -16),
			imageOffset: new AMap.Pixel(-1, 0),
			textColor: "#fff",
			textSize:9
		}, {
			url: img_url[0],
			size: new AMap.Size(56, 21),
			offset: new AMap.Pixel(-16, -16),
			imageOffset: new AMap.Pixel(-1, 0),
			textColor: "#fff",
			textSize:9
		}, {
			url: img_url[0],
			size: new AMap.Size(56, 21),
			offset: new AMap.Pixel(-16, -16),
			imageOffset: new AMap.Pixel(-1, 0),
			textColor: "#fff",
			textSize:9
		}
	];

		var _renderMarker = function(context) {
			var infomarker = new AMap.Marker({content: ' ', map: map});
			// const keys = Object.keys(data.clusterData); // 聚合中包含数据
			var icon = img_url[1];
			var offset = new AMap.Pixel(-1, -1);
			var size = new AMap.Size(21, 21);
			context.marker.setSize(size);
			context.marker.setIcon(icon);
			context.marker.setOffset(offset);
			// 鼠标移入事件
			context.marker.on('mouseover', function(){
				var clusterData = context.data[0]
				var position = clusterData.name && clusterData.lnglat;
					// console.log(clusterData);
					if(position){
						infomarker.setPosition(clusterData.lnglat);
						infomarker.setLabel({
							content: clusterData.name,
							offset:[22,10,0,0],
						});
						map.add(infomarker);
						const markerLabel = document.querySelector('.amap-marker-label')
						markerLabel.style.lineHeight = "20px";
						markerLabel.style.fontSize = "14px";
						// console.log(markerLabel.style)
					}
			});
			// 鼠标移出事件
			context.marker.on('mouseout', function(){
				map.remove(infomarker);
			});
		}
			cluster = new AMap.MarkerCluster(map, data, {
				styles: sts,
				gridSize: 70,
				zIndex: 50,
				renderMarker:_renderMarker
			});

		cluster.on('click', function(e) {
			let zoom = map.getZoom();
			if (zoom <= 11) {
				map.setZoomAndCenter(15, e.lnglat);
			}else if(zoom >= 11 && zoom <= 15){
				map.setZoomAndCenter(18, e.lnglat);
			}else{
				// console.log(cluster.context)
				map.setZoomAndCenter(20, e.lnglat);
			}

		});
		return cluster
	}
	// 索引海量点聚合
	var indexMarkers_count = marks_keyUnit.length;
	//聚合点
	function clusterMarkers() {
        var markerList = [];
        var marker = new AMap.Marker({
            map: map,
        })
        markerList.push(marker);
        return markerList;
    }
	var clusterIndexSet = {
        city: {
            minZoom: 2,
            maxZoom: 10.5,
        },
        dadui: {
            minZoom: 10.5,
            maxZoom: 15,
        },
        name: {
            minZoom: 15,
            maxZoom: 22,
        },
    };
	function getStyle(context) {
        var clusterData = context.clusterData; // 聚合中包含数据
        var index = context.index; // 聚合的条件
        var count = context.count; // 聚合中点的总数
        var marker = context.marker; // 聚合绘制点 Marker 对象
        var color = [
            '8,60,156',
			'66,130,198',
            '78,200,211',
        ];
        var indexs = ['city','dadui','name'];
        var i = indexs.indexOf(index['mainKey']);
		// console.log(i)
		// console.log(index['mainKey'])
		// console.log(clusterData)
        var text = clusterData[0][index['mainKey']];
		//圈大小
        var size = Math.round(30 + Math.pow(count / marks_keyUnit.length, 1 / 5) * 90);
        if(i <= 1){
            var extra = '<span class="showCount">'+ context.count +'家</span>';
            text = '<span class="showName">'+ text +'</span>';
            text += extra;
        } else {
            size = 16 * text.length + 20;
        }
        var style = {
            bgColor: 'rgba(' + color[i] + ',.8)',
            borderColor: 'rgba(' + color[i] + ',1)',
            text: text,
            size: size,
            index: i,
            color: '#ffffff',
            textAlign: 'center',
            boxShadow: '0px 0px 5px rgba(0,0,0,0.8)',
			display: 'flex',
			flexWrap: 'wrap',
			placeContent: 'center',
			alignItems: 'center',

        }
        return style;
    }
	function getPosition(context) {
        var key = context.index.mainKey;
        var dataItem = context.clusterData && context.clusterData[0];
        var districtName = dataItem[key];
        if(!district[districtName]) {
            return null;
        }
        var center = district[districtName].center.split(',');
        var centerLnglat = new AMap.LngLat(center[0], center[1]);
        return centerLnglat;
    }
	function _customRender (data) {
        const keys = Object.keys(data.clusterData);
        let markers = [];
        for (var i = 0; i < keys.length; i++) {
            var key = keys[i];
            var cluster = data.clusterData[key];
            var position = cluster.data[0].lnglat;
            var marker = new AMap.LabelMarker({
                position: position,
                text: {
                    content: cluster.data.length.toString(),
                    style: {
                        fillColor: '#ffffff'
                    }
                }
            });
            markers.push(marker)
        }
        return {
            type: 'type',
            layer: null,
            markers: markers,
        };
    }
    // 自定义聚合点样式
    function _renderClusterMarker (context) {
        var clusterData = context.clusterData; // 聚合中包含数据
        var index = context.index; // 聚合的条件
        var count = context.count; // 聚合中点的总数
        var marker = context.marker; // 聚合点标记对象
        var styleObj = getStyle(context);
        // 自定义点标记样式
        var div = document.createElement('div');
        div.className = 'amap-cluster';
        div.style.backgroundColor = styleObj.bgColor;
        div.style.width = styleObj.size + 'px';
        if(styleObj.index <= 1) {
            div.style.height = styleObj.size + 'px';
            // 自定义点击事件
            context.marker.on('click', function(e) {
                // console.log(e)
                var curZoom = map.getZoom();
                if(curZoom < 20){
                    curZoom += 3;
                }
                map.setZoomAndCenter(curZoom, e.lnglat);
            });
        }
        div.style.border = 'solid 1px ' + styleObj.borderColor;
        div.style.borderRadius = styleObj.size + 'px';
        div.innerHTML = styleObj.text;
        div.style.color = styleObj.color;
        div.style.textAlign = styleObj.textAlign;
        div.style.boxShadow = styleObj.boxShadow;
		div.style.display = styleObj.display;
		div.style.flexWrap = styleObj.flexWrap;
		div.style.placeContent = styleObj.placeContent;
		div.style.alignItems = styleObj.alignItems;

        context.marker.setContent(div)
        // 自定义聚合点标记显示位置
        var position = getPosition(context);
        if(position){
            context.marker.setPosition(position);
        }
        context.marker.setAnchor('center');

    };

    // var cluster0 = new AMap.IndexCluster(map, marks_keyUnit, {
    //     renderClusterMarker: _renderClusterMarker,
    //     clusterIndexSet: clusterIndexSet,
    // });

	//判断浏览区是否支持canvas
    function isSupportCanvas() {
        var elem = document.createElement('canvas');
        return !!(elem.getContext && elem.getContext('2d'));
    }
	//火灾热力图
	if (!isSupportCanvas()) {
        alert('热力图仅对支持canvas的浏览器适用,您所使用的浏览器不能使用热力图功能,请换个浏览器试试~')
    }

    //详细的参数,可以查看heatmap.js的文档 http://www.patrick-wied.at/static/heatmapjs/docs.html
    //参数说明如下:
    /* visible 热力图是否显示,默认为true
     * opacity 热力图的透明度,分别对应heatmap.js的minOpacity和maxOpacity
     * radius 势力图的每个点的半径大小
     * gradient  {JSON} 热力图的渐变区间 . gradient如下所示
     *	{
     .2:'rgb(0, 255, 255)',
     .5:'rgb(0, 110, 255)',
     .8:'rgb(100, 0, 255)'
     }
     其中 key 表示插值的位置, 0-1
     value 为颜色值
     */
	var json_fire_heatmapData  = fire_heatmapData.map(function(item) {
		var lnglat = JSON.parse(item);
		var gcj02 = wgs84togcj02(lnglat['lng'],lnglat['lat']);
		lnglat['lng'] = gcj02[0];
		lnglat['lat'] = gcj02[1];
		return lnglat;
	  });

	var fire_heatmap = new AMap.HeatMap(map, {
		radius: 12, //给定半径
		visible: false,
		opacity: [0.5, 0.8],
		gradient:{
				// 0.05: 'blue',
				// 0.08: 'rgb(117,211,248)',
				0.05: 'rgb(0, 255, 0)',
				0.1: '#ffea00',
				0.2: 'red',
				1: 'red',
			}
	});


	// 添加多边形
	function addPolygon(data, color,info,type=-1) {
		let polygon = new AMap.Polygon({
		  path: data,
		  fillColor: color,
		  strokeOpacity: 1,
		  fillOpacity: 0.6,
		  strokeColor: '#00D3FC',
		  strokeWeight: 2,
		  strokeStyle: 'dashed',
		  strokeDasharray: [5, 5],
		});

		var marker = new AMap.Marker({content: ' ', map: map});

		polygon.on('mouseover', () => {
		  polygon.setOptions({
			fillOpacity: 0.8,
			fillColor: color

		  })

		})
		polygon.on('click', (e) => {
			marker.setPosition(e.lnglat);
			marker.setLabel({content: info});
			marker.show();
			//信息
			url = 'http://'+host+'/webroot/decision/view/form?viewlet=31%25E9%2598%25B2%25E7%2581%25AB%25E4%25B8%259A%25E5%258A%25A1%252F%25E5%258F%258C%25E9%259A%258F%25E6%259C%25BA%252F%25E5%258F%258C%25E9%259A%258F%25E6%259C%25BA.frm&ref_t=design&ref_c=64cda710-240d-4535-80cb-a5bfbc0a0578&dadui=' + info
			if (type === 0) {
				window.open(url);
			}
		})
		polygon.on('mouseout', () => {
		  polygon.setOptions({
			fillOpacity: 0.6,
			fillColor: color
		  })
		marker.hide();
		})
		polygon.setMap(map);
		// map.add(polygon);
		// polygon.hide();
		return polygon
	}

	//添加文本
	function addText(lnglat,info) {
		var text = new AMap.Text({
			text:info,
			anchor:'center', // 设置文本标记锚点
			draggable:false, //不可拖动
			cursor:'pointer',
			style:{
				'background-color': 'rgba(255, 255, 255, 0)',
				'width': '15rem',
				'border-width': 0,
				// 'box-shadow': '0 2px 6px 0 rgba(114, 124, 245, .5)',
				'text-align': 'center',
				'font-size': '17px',
				'color': '#fff'
			},
			position: lnglat
		});
		text.setMap(map);
		text.on('click', (e) => {
			url = 'http://'+host+'/webroot/decision/view/form?viewlet=31%25E9%2598%25B2%25E7%2581%25AB%25E4%25B8%259A%25E5%258A%25A1%252F%25E5%258F%258C%25E9%259A%258F%25E6%259C%25BA%252F%25E5%258F%258C%25E9%259A%258F%25E6%259C%25BA.frm&ref_t=design&ref_c=64cda710-240d-4535-80cb-a5bfbc0a0578&dadui=' + e.target._originOpts.text
			window.open(url);
			//信息
		})
		return text
	}

	//清除文本
	function cleanText(textList) {
		for (let index = 0; index < textList.length; index++) {
			textList[index].setMap(null);
		}
	}

	//大队多边形
	var color_rank = ['#c24b50','#c45154','#c65658','#c75758','#cc6460','#cd6561',
		'#cd6762','#cf6d66','#d06f67','#d17269','#d3756b','#d4796d','#da8a77','#e9c090',
		'#ebc793','#eccb95','#ebc793','#efd59a','#f1dc9d','#f1dd9d','#f3e3a0','#f3e5a1']

	fire_heatmap.setDataSet({
		data: json_fire_heatmapData,
		max: 100
	});
	fire_heatmap.hide()

	//实时路况图层
    var trafficLayer = new AMap.TileLayer.Traffic({
        zIndex: 10,
        zooms: [7, 22],
		visible: false,
    });

	trafficLayer.setMap(map);

	//地图热点信息
	var placeSearch = new AMap.PlaceSearch();  //构造地点查询类
    var placeSearchinfoWindow=new AMap.InfoWindow({
		offset:[0,-20]
	});
    map.on('hotspotclick', function(result) {
        placeSearch.getDetails(result.id, function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
                placeSearch_CallBack(result);
            }
        });
    });
    //回调函数
    function placeSearch_CallBack(data) { //infoWindow.open(map, result.lnglat);
        var poiArr = data.poiList.pois;
        if(poiArr[0]){
          var location = poiArr[0].location;
          placeSearchinfoWindow.setContent(createContent(poiArr[0]));
          placeSearchinfoWindow.open(map,location);
        }
    }
    function createContent(poi) {  //信息窗体内容
        var s = [];
        s.push('<div class="info-title">'+poi.name+'</div><div class="info-content">'+"地址：" + poi.address);
        s.push("电话：" + poi.tel);
        s.push("类型：" + poi.type);
        s.push('<div>');
        return s.join("<br>");
    }


	// var iotMarks = addMassMarks(marks_iot);
	// var tietaMarks = addMassMarks(marks_tieta);
	// var water1Marks = addMassMarks(marks_water1);
	// var warehouseMarks = addMassMarks(marks_warehouse);
	// var FireStationMarks = addMassMarks(marks_miniFireStation)
	// var professionalFireMarks = addMassMarks(marks_professionalFire);
	// var fireServiceMarks = addMassMarks(marks_fireService);
	// var jiedaoMarks = addMassMarks(marks_jiedao);
	// var psbMarks = addMassMarks(marks_psb);
	// var jyzMarks = addMassMarks(marks_jyz);
	// var daduiMarks = addMassMarks(marks_dadui);

	//按钮控制开关
    let workstation_state = false;
	document.querySelectorAll('input[type="checkbox"]').forEach(function(checkbox) {
		checkbox.addEventListener('input', function(event) {
			// 在这里处理点击事件
			switch (event.target.id) {
				case "dadui_btn":
				case "dadui_btn1":
					if (event.target.checked) {
						// 将大队添加至地图实例 全局变量
						document.getElementById('dadui_btn').checked = true
						document.getElementById('dadui_btn1').checked = true
						daduiMarks = addMassMarks(marks_dadui);
					}else{
						// daduiMarks.hide();
						document.getElementById('dadui_btn').checked = false
						document.getElementById('dadui_btn1').checked = false
						daduiMarks.clear();
					}
					break;
				case "f-btn1":
					if (event.target.checked) {
						// 将救援站添加至地图实例
						jyzMarks = addMassMarks(marks_jyz);
					}else{
						jyzMarks.clear();
					}
					break;
				case "water0-btn":
					if (event.target.checked) {
						// 消火栓
						cluster0 = addCluster(["http://"+host+"/webroot/static/map/picture/xiaohuoshuan1.png","http://"+host+"/webroot/static/map/picture/xiaohuoshuan.png"],marks_water0);
					}else{

						cluster0.setMap(null);
					}
					break;
				case "water1-btn":
					if (event.target.checked) {
						// 消防水鹤
						water1Marks = addMassMarks(marks_water1);
					}else{
						water1Marks.clear();
					}
					break;
				case "warehouse-btn":
					if (event.target.checked) {
						// 库房
						warehouseMarks = addMassMarks(marks_warehouse);
					}else{
						warehouseMarks.clear();
					}
					break;
				case "fireStation_btn":
					if (event.target.checked) {
						// 微型消防站
						cluster1 = addCluster(["http://"+host+"/webroot/static/map/picture/weixingxiaofang1.png","http://"+host+"/webroot/static/map/picture/weixingxiaofang.png"],marks_miniFireStation);
					}else{
						cluster1.setMap(null);
						// addCluster(showHide = true);
					}
					break;
				case "keyUnit_btn":
				case "keyUnit_btn1":
					if (event.target.checked) {
						// 重点单位
						document.getElementById('keyUnit_btn').checked = true
						document.getElementById('keyUnit_btn1').checked = true
						keyUnitMarks = addMassMarks(marks_keyUnit);
					}else{
						document.getElementById('keyUnit_btn').checked = false
						document.getElementById('keyUnit_btn1').checked = false
						keyUnitMarks.clear();
					}
					break;
				case "keyUnit_btn2":
					if (event.target.checked) {
						// 重点单位
						keyUnitMarks1 = addMassMarks(marks_keyUnit_assessment);
					}else{
						keyUnitMarks1.clear();
					}
					break;
				case "professionalFire_btn":
					if (event.target.checked) {
						// 企业专职消防队
						professionalFireMarks = addMassMarks(marks_professionalFire);
					}else{
						professionalFireMarks.clear();
					}
					break;
				case "fireCar_btn":
					if (event.target.checked) {
						// 执勤车辆定位
						var car_location = _g().getWidgetByName('refresh_data').getCell(1,3).getValue();
						var car_location0 = JSON.parse(car_location)['value'];
						var car_location1 = JSON.parse('['+ car_location0 + ']');
						var car_location1  = car_location1.map(function(item) {
							var gcj02 = wgs84togcj02(item['lnglat'][0],item['lnglat'][1]);
							item['lnglat'][0] = gcj02[0];
							item['lnglat'][1] = gcj02[1];
							return item;
						  });
						car_locationMarks = addMassMarks(car_location1);
					}else{
						car_locationMarks.clear();
					}
					break;
				case "psb_btn":
					if (event.target.checked) {
						// 派出所
						psbMarks = addMassMarks(marks_psb);
					}else{
						psbMarks.clear();
					}
					break;
				case "jiedao_btn":
					if (event.target.checked) {
						// 街道
						jiedaoMarks = addMassMarks(marks_jiedao);
					}else{
						jiedaoMarks.clear();
					}
					break;
				case "fireService_btn":
					if (event.target.checked) {
						// 服务机构
						fireServiceMarks = addMassMarks(marks_fireService);
					}else{
						fireServiceMarks.clear();
					}
					break;
				case "tieta_btn":
				case "tieta_btn1":
					if (event.target.checked) {
						document.getElementById('tieta_btn').checked = true
						document.getElementById('tieta_btn1').checked = true
						// 铁塔点位
						tietaMarks = addMassMarks(marks_tieta);
					}else{
						document.getElementById('tieta_btn').checked = false
						document.getElementById('tieta_btn1').checked = false
						tietaMarks.clear();
					}
					break;
				case "iot_btn":
					if (event.target.checked) {
						// 铁塔点位
						iotMarks = addMassMarks(marks_iot);
					}else{
						iotMarks.clear();
					}
					break;
				case "CivRescue_btn":
					if (event.target.checked) {
						// 民间救援组织
						civilianRescueMarks = addMassMarks(marks_civilianRescue);
					}else{
						civilianRescueMarks.clear();
					}
					break;
				case "servicesUnit_btn":
					if (event.target.checked) {
						// 联勤保障单位
						serverUnitMarks = addMassMarks(marks_serverUnit);
					}else{
						serverUnitMarks.clear();
					}
					break;
				case "traffic_btn":
					if (event.target.checked) {
						// 实时路况
						trafficLayer.show();
					}else{
						trafficLayer.hide();
					}
					break;
				case "liveToastBtn":
					const toastLiveExample = document.getElementById('liveToast')
					if (event.target.checked) {
						// 将救援站添加至地图实例
						const toast = new bootstrap.Toast(toastLiveExample)
						toast.show()
					}
					break;
				case "daduiPolygonBtn":
					if (event.target.checked) {
						//大队辖区添加至地图实例
						daduiPolygon_list = [];
						daduiText_list = [];
						for (let rank_index = 0; rank_index < dadui_polygon.length; rank_index++) {
							// console.log(dadui_polygon_clean['features'][rank_index]['properties']['name'])
							var dadui_info = JSON.parse(dadui_polygon[rank_index]);

							var dadui_info_geo = JSON.parse(dadui_info['geo']);
							dadui_info_geo0 = dadui_info_geo['features'][0]['geometry']['coordinates'][0];

							var daduiPolygon = addPolygon(dadui_info_geo0,dadui_info['color'],dadui_info['name'],dadui_info['type']);
							daduiPolygon_list.push(daduiPolygon);

							if (dadui_info['name'] === '保税区大队') {
								dadui_info_geo1 = dadui_info_geo['features'][1]['geometry']['coordinates'][0];
								var daduiPolygon0 = addPolygon(dadui_info_geo1,dadui_info['color'],dadui_info['name'],dadui_info['type']);
								daduiPolygon_list.push(daduiPolygon0);
								//添加文字说明
								// daduiText = addText([122.043377,39.253099],dadui_info['name']);
								// daduiText_list.push(daduiText);
							}
							//添加文字说明
							// daduiText = addText(dadui_info['center'].split(","),dadui_info['name']);
							// daduiText_list.push(daduiText);
						}

					}else{
						for (let rank_index = 0; rank_index < daduiPolygon_list.length; rank_index++) {
							daduiPolygon_list[rank_index].setMap(null);
						}
						cleanText(daduiText_list);
					}
					break;
				case "jyzPolygonBtn":
					if (event.target.checked) {
						// 将救援站辖区添加至地图实例
						//救援站辖区
						jyzPolygon_list = []
						for (let rank_index = 0; rank_index < jyz_polygon.length; rank_index++) {
							// console.log(dadui_polygon_clean['features'][rank_index]['properties']['name'])
							jyz_info = JSON.parse(jyz_polygon[rank_index])
							if (['羊头洼消防救援站','七贤路消防救援站','宝强街消防救援站'].indexOf(jyz_info['name']) == -1 ) {
								jyz_info_geo = JSON.parse(jyz_info['geo'])
							}else{
								jyz_info_geo = JSON.parse(jyz_info['geo'])
								jyz_info_geo = jyz_info_geo['features'][0]['geometry']['coordinates'][0]
								// console.log(jyz_info_geo)
							}
							var jyzPolygon = addPolygon(jyz_info_geo,jyz_info['color'],jyz_info['name'])
							jyzPolygon_list.push(jyzPolygon)
						}
					}else{
						for (let rank_index = 0; rank_index < jyzPolygon_list.length; rank_index++) {
							jyzPolygon_list[rank_index].setMap(null);
						}
					}
					break;
				case "jiedaoPolygonBtn":
					if (event.target.checked) {
						//街道辖区添加至地图实例
						jiedaoPolygon_list = []
						for (let index = 0; index < jiedao_polygon.length; index++) {
							var jiedao_info = JSON.parse(jiedao_polygon[index]);
							//街道数组内部geojson
							var jiedao_info_geo = JSON.parse(jiedao_info['geo']);
							var jiedao_info_feature = jiedao_info_geo['features']
							for (let index0 = 0; index0 < jiedao_info_feature.length; index0++) {
								var jiedao_info_geo0 = jiedao_info_feature[index0];
								var jiedaoPolygon0 = addPolygon(jiedao_info_geo0['geometry']['coordinates'],color_list[Math.floor(Math.random() * 6)],jiedao_info['name']+"（"+jiedao_info_geo0['properties']['name']+"）");
								jiedaoPolygon_list.push(jiedaoPolygon0);
							}
						}
					}else{
						for (let rank_index = 0; rank_index < jiedaoPolygon_list.length; rank_index++) {
							jiedaoPolygon_list[rank_index].setMap(null);
						}
					}
					break;
				case "fireHeatmap_btn":
						if (event.target.checked) {
							// 将救援站添加至地图实例
							fire_heatmap.show();
						}else{
							fire_heatmap.hide();
						}
						break;
                case "fire_construction_btn":
						if (event.target.checked) {
							// 消防工作站添加至地图实例
							workStationMarks = addMassMarks(marks_workstation);
							workstation_state = true;
							window.parent.postMessage(workstation_state, "http://"+host);
							console.log(workstation_state);
						}else{
							workStationMarks.clear()
							workstation_state = false;
							window.parent.postMessage(workstation_state, "http://"+host);
							console.log(workstation_state);
						}
						break;
				default:
					break;
			}
		});
	});



	//修改地图样式
	function setMapStyle(value) {
		// map.clearMap();
		var styleName = "amap://styles/" + value;
		map.setMapStyle(styleName);
		if (value == 'norma') {
			map.setZoomAndCenter(14, map_center);
		}else if(value == 'dark'){
			map.setZoomAndCenter(9.2, [122.037937,39.417]);
			fire_heatmap.show();
		}

	  }

	//画圆
	function draw_circle(position) {
		var circleEditor = new AMap.CircleEditor(map, position,
			{movePoint:{
			anchor:'center',
			zIndex: 20,
			icon: 'http://'+host+'/webroot/static/map/fire.png',
			},
		});
		return circleEditor
	}

	//计算点与点（数组）距离
	function get_distance(p1,p2_list,range) {
		var result_list = [];
		for (let index = 0; index < p2_list.length; index++) {
			var p2_info = p2_list[index];
			const p2 = p2_info['lnglat'];
			// var distance = Math.round(p1.distance(p2));
			var distance = p1.distance(p2);

			if (distance <= range) {
				result_list.push(p2_info);
			}
		}
		return result_list;
	}



	//火灾定位
	var overlays = [];
	var draw_circleEditor;
	var mouseTool = new AMap.MouseTool(map);
	//监听draw事件可获取画好的覆盖物
	mouseTool.on('draw',function(e){
		// overlays.push(e.obj);
		console.log(e);
		map.setFitView([ e.obj ])
		// //计算500米内水源
		// var water_list = get_distance(e.obj._position,marks_water0,500);
		// // console.log(water_list.length);
		draw_circleEditor = draw_circle(e.obj);

		draw_circleEditor.open();
		mouseTool.close();
		map.setDefaultCursor('auto');
	})
	//h2标签点击监控
	document.querySelectorAll('button[type="button"]').forEach(function(btn) {
		btn.addEventListener('click', function(event) {
			// 在这里处理点击事件
			switch (event.target.id) {
				case "headingBtnOne":
					// setMapStyle("normal");
					break;
				case "headingBtnTwo1":
					// setMapStyle("dark");
					break;
				case "headingBtnThree1":
					// setMapStyle("blue");
					break;

				default:
					break;
			}
			// console.log('Checkbox clicked', );
		});
	});

	//搜索按钮监控
	var button_serch = document.getElementById("fr-btn-BUTTON_SERCH");

	//坐标搜索
		// 实例化AutoComplete
	var autoOptions = {
		city: "大连",
		citylimit: true,
		input: "search_input"
	};
	var autoComplete = new AMap.AutoComplete(autoOptions);
	var placeSearch = new AMap.PlaceSearch({
		pageSize: 5, // 单页显示结果条数
		pageIndex: 1, // 页码
		city: "大连", // 兴趣点城市
		citylimit: true,  //是否强制限制在设置的城市内搜索
		map: map, // 展现结果的地图实例
		// panel: "panel", // 结果列表将在此容器中进行展示。
		autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
	});  //构造地点查询类
	autoComplete.on("select", select);//注册监听，当选中某条记录时会触发
	function select(e) {
		placeSearch.setCity(e.poi.adcode);
		placeSearch.search(e.poi.name);  //关键字查询查询
	}

	// 为该元素添加点击事件监听器
	button_serch.addEventListener("click", function(event) {
		var keywords = document.getElementById("search_input").value;
		placeSearch.search(keywords);
	});


	// 火灾定位和清除覆盖物
	var fire_location = document.getElementById("fr-btn-FIRE_LOCATION");
	// 为该元素添加点击事件监听器
	fire_location.addEventListener("click", function(event) {
		// 处理点击事件
		map.setDefaultCursor('url(http://'+host+'/webroot/static/map/fire.png),not-allowed');
		// mouseTool.marker({
		// 	anchor:'center',
		// 	icon: 'http://'+host+'/webroot/static/map/fire.png',
		// });
		mouseTool.circle({
			// center: position,
			radius: 500, //半径
			borderWeight: 3,
			strokeColor: "#FF33FF",
			strokeOpacity: 1,
			strokeWeight: 6,
			strokeOpacity: 0.2,
			fillOpacity: 0.4,
			// strokeStyle: 'dashed',
			strokeDasharray: [10, 10],
			// 线样式还支持 'dashed'
			fillColor: '#1791fc',
			zIndex: 10,
		});
	});
	// -------
	var clean_map = document.getElementById("fr-btn-CLEAN_MAP");
	// 为该元素添加点击事件监听器
	clean_map.addEventListener("click", function(event) {
		// 处理点击事件
		map.clearMap();
		trafficLayer.hide();
		fire_heatmap.hide();
		document.querySelectorAll('input[type="checkbox"]').forEach(function(checkbox) {
			checkbox.checked = false;
		});
		if (typeof cluster0 !== 'undefined') {
			cluster0.setMap(null);
		}
		if (typeof cluster1 !== 'undefined') {
			cluster1.setMap(null);
		}
		draw_circleEditor.close();
	});


	})

	.catch((e) => {
		console.error(e); //加载错误提示
	});
  }, 100)