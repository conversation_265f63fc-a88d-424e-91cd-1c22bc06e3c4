#!/usr/bin/env python3
"""
RTMP转HLS转换工具
将RTMP流转换为HLS格式，供浏览器播放
"""

import subprocess
import threading
import time
import os
import http.server
import socketserver
from pathlib import Path

class RTMPToHLSConverter:
    def __init__(self, rtmp_url, output_dir="./hls_output", hls_time=2):
        self.rtmp_url = rtmp_url
        self.output_dir = Path(output_dir)
        self.hls_time = hls_time
        self.ffmpeg_process = None
        self.http_server = None
        self.is_running = False
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
    def start_conversion(self):
        """开始RTMP到HLS转换"""
        print(f"开始转换: {self.rtmp_url} -> HLS")
        
        output_file = self.output_dir / "stream.m3u8"
        
        # FFmpeg命令
        cmd = [
            'ffmpeg',
            '-i', self.rtmp_url,
            '-c:v', 'libx264',          # 视频编码器
            '-c:a', 'aac',              # 音频编码器
            '-preset', 'ultrafast',      # 编码速度
            '-tune', 'zerolatency',      # 低延迟调优
            '-f', 'hls',                # 输出格式
            '-hls_time', str(self.hls_time),        # 每个片段时长
            '-hls_list_size', '6',       # 播放列表中保留的片段数
            '-hls_flags', 'delete_segments',  # 删除旧片段
            '-hls_allow_cache', '0',     # 不允许缓存
            '-hls_segment_filename', str(self.output_dir / 'segment_%03d.ts'),
            str(output_file),
            '-y'  # 覆盖输出文件
        ]
        
        try:
            print("启动FFmpeg转换...")
            print(f"命令: {' '.join(cmd)}")
            
            self.ffmpeg_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            self.is_running = True
            print("✓ FFmpeg转换已启动")
            
            # 监控FFmpeg输出
            def monitor_ffmpeg():
                while self.is_running and self.ffmpeg_process:
                    if self.ffmpeg_process.poll() is not None:
                        break
                    time.sleep(1)
                
                if self.ffmpeg_process:
                    stdout, stderr = self.ffmpeg_process.communicate()
                    if stderr:
                        print(f"FFmpeg错误: {stderr}")
            
            threading.Thread(target=monitor_ffmpeg, daemon=True).start()
            return True
            
        except FileNotFoundError:
            print("错误: 未找到FFmpeg，请先安装FFmpeg")
            return False
        except Exception as e:
            print(f"启动转换失败: {e}")
            return False
    
    def start_http_server(self, port=8000):
        """启动HTTP服务器提供HLS文件"""
        try:
            os.chdir(self.output_dir)
            
            class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
                def end_headers(self):
                    # 添加CORS头
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', '*')
                    # 设置缓存策略
                    if self.path.endswith('.m3u8'):
                        self.send_header('Cache-Control', 'no-cache')
                    elif self.path.endswith('.ts'):
                        self.send_header('Cache-Control', 'max-age=3600')
                    super().end_headers()
            
            handler = CustomHTTPRequestHandler
            self.http_server = socketserver.TCPServer(("", port), handler)
            
            def serve():
                print(f"✓ HTTP服务器启动在端口 {port}")
                print(f"HLS播放地址: http://127.0.0.1:{port}/stream.m3u8")
                self.http_server.serve_forever()
            
            threading.Thread(target=serve, daemon=True).start()
            return True
            
        except Exception as e:
            print(f"启动HTTP服务器失败: {e}")
            return False
    
    def stop(self):
        """停止转换和服务器"""
        self.is_running = False
        
        if self.ffmpeg_process:
            print("停止FFmpeg转换...")
            self.ffmpeg_process.terminate()
            try:
                self.ffmpeg_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.ffmpeg_process.kill()
            self.ffmpeg_process = None
        
        if self.http_server:
            print("停止HTTP服务器...")
            self.http_server.shutdown()
            self.http_server = None
        
        print("转换已停止")
    
    def wait_for_hls(self, timeout=30):
        """等待HLS文件生成"""
        m3u8_file = self.output_dir / "stream.m3u8"
        
        print("等待HLS文件生成...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if m3u8_file.exists() and m3u8_file.stat().st_size > 0:
                print("✓ HLS文件已生成")
                return True
            time.sleep(1)
        
        print("⚠️ 等待HLS文件超时")
        return False

def main():
    print("RTMP转HLS转换工具")
    print("=" * 50)
    
    # 配置
    rtmp_url = input("输入RTMP地址 (默认: rtmp://127.0.0.1:1935/live/stream): ").strip()
    if not rtmp_url:
        rtmp_url = "rtmp://127.0.0.1:1935/live/stream"
    
    port = input("HTTP服务器端口 (默认: 8000): ").strip()
    if not port:
        port = 8000
    else:
        port = int(port)
    
    # 创建转换器
    converter = RTMPToHLSConverter(rtmp_url)
    
    try:
        # 启动转换
        if not converter.start_conversion():
            return
        
        # 启动HTTP服务器
        if not converter.start_http_server(port):
            return
        
        # 等待HLS文件生成
        if converter.wait_for_hls():
            print("\n" + "=" * 50)
            print("🎉 转换成功！")
            print(f"📺 在浏览器中打开: http://127.0.0.1:{port}/stream.m3u8")
            print(f"🌐 或使用HTML测试页面: rtmp_test.html")
            print("=" * 50)
            print("\n按 Ctrl+C 停止服务...")
            
            # 保持运行
            while True:
                time.sleep(1)
        else:
            print("HLS文件生成失败，请检查RTMP流是否正常")
    
    except KeyboardInterrupt:
        print("\n用户中断服务")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        converter.stop()

if __name__ == "__main__":
    main()
