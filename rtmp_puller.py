import cv2
import threading
import time
import os
from datetime import datetime

class RTMPPuller:
    def __init__(self, rtmp_url, output_dir="./output"):
        self.rtmp_url = rtmp_url
        self.output_dir = output_dir
        self.cap = None
        self.is_running = False
        self.frame_count = 0
        self.start_time = None
        
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def connect(self, timeout=10):
        """连接到RTMP流"""
        print(f"正在连接到: {self.rtmp_url}")
        print("提示: 如果连接失败，请检查:")
        print("  1. RTMP服务器是否运行")
        print("  2. 流地址是否正确")
        print("  3. 防火墙设置")

        # 设置连接参数
        self.cap = cv2.VideoCapture()

        # 设置缓冲区大小和超时
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

        # 尝试打开流
        success = self.cap.open(self.rtmp_url)

        if not success or not self.cap.isOpened():
            print("错误: 无法连接到RTMP流")
            print("可能的解决方案:")
            print("  1. 检查RTMP服务器是否在运行")
            print("  2. 尝试其他流地址，如: rtmp://127.0.0.1:1935/live/stream")
            print("  3. 使用VLC等工具测试流是否可用")
            return False

        # 尝试读取第一帧来验证连接
        print("验证流连接...")
        ret, frame = self.cap.read()
        if not ret:
            print("错误: 无法读取流数据")
            return False

        # 获取流信息
        fps = self.cap.get(cv2.CAP_PROP_FPS)
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        print(f"连接成功!")
        print(f"分辨率: {width}x{height}")
        print(f"帧率: {fps} FPS")

        return True
    
    def start_pulling(self, save_video=False, show_preview=True):
        """开始拉流"""
        if not self.connect():
            return
        
        self.is_running = True
        self.start_time = time.time()
        
        # 如果需要保存视频，设置视频写入器
        video_writer = None
        if save_video:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = os.path.join(self.output_dir, f"stream_{timestamp}.mp4")
            
            fps = self.cap.get(cv2.CAP_PROP_FPS) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            print(f"视频将保存到: {output_path}")
        
        print("开始拉流... 按 'q' 退出")
        
        try:
            while self.is_running:
                ret, frame = self.cap.read()
                
                if not ret:
                    print("警告: 无法读取帧，可能是流中断")
                    time.sleep(0.1)
                    continue
                
                self.frame_count += 1
                
                # 保存视频帧
                if save_video and video_writer:
                    video_writer.write(frame)
                
                # 显示预览
                if show_preview:
                    # 添加信息文本
                    elapsed_time = time.time() - self.start_time
                    info_text = f"Frame: {self.frame_count} | Time: {elapsed_time:.1f}s"
                    cv2.putText(frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 
                               0.7, (0, 255, 0), 2)
                    
                    cv2.imshow('RTMP Stream', frame)
                    
                    # 检查退出键
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                # 打印统计信息
                if self.frame_count % 100 == 0:
                    elapsed_time = time.time() - self.start_time
                    fps_actual = self.frame_count / elapsed_time if elapsed_time > 0 else 0
                    print(f"已处理 {self.frame_count} 帧, 实际FPS: {fps_actual:.2f}")
        
        except KeyboardInterrupt:
            print("\n用户中断拉流")
        
        finally:
            self.stop()
            if video_writer:
                video_writer.release()
            if show_preview:
                cv2.destroyAllWindows()
    
    def stop(self):
        """停止拉流"""
        self.is_running = False
        if self.cap:
            self.cap.release()
        print("拉流已停止")
    
    def save_screenshot(self):
        """保存当前帧截图"""
        if self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            if ret:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = os.path.join(self.output_dir, f"screenshot_{timestamp}.jpg")
                cv2.imwrite(screenshot_path, frame)
                print(f"截图已保存: {screenshot_path}")
                return screenshot_path
        return None

def test_rtmp_connection(rtmp_url):
    """测试RTMP连接"""
    print(f"测试连接: {rtmp_url}")

    # 使用ffmpeg命令测试（如果可用）
    import subprocess
    try:
        # 测试流是否存在
        cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_streams', rtmp_url]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ FFprobe 检测到流")
            return True
        else:
            print("✗ FFprobe 无法检测到流")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠ FFprobe 不可用，使用OpenCV测试")

    # 使用OpenCV测试
    cap = cv2.VideoCapture()
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

    if cap.open(rtmp_url):
        ret, _ = cap.read()
        cap.release()
        if ret:
            print("✓ OpenCV 可以读取流")
            return True
        else:
            print("✗ OpenCV 无法读取流数据")
    else:
        print("✗ OpenCV 无法连接到流")

    return False

def main():
    # RTMP流地址
    rtmp_url = "rtmp://127.0.0.1/live/stream"

    print("RTMP拉流工具")
    print("=" * 50)
    print("1. 仅预览 (不保存)")
    print("2. 预览 + 保存视频")
    print("3. 仅保存视频 (不预览)")
    print("4. 保存截图")
    print("5. 测试连接")
    print("6. 自定义RTMP地址")

    try:
        choice = input("请选择模式 (1-6): ").strip()

        if choice == "6":
            rtmp_url = input("请输入RTMP地址: ").strip()
            if not rtmp_url:
                print("地址不能为空")
                return

        # 创建拉流器
        puller = RTMPPuller(rtmp_url)

        if choice == "1":
            puller.start_pulling(save_video=False, show_preview=True)
        elif choice == "2":
            puller.start_pulling(save_video=True, show_preview=True)
        elif choice == "3":
            puller.start_pulling(save_video=True, show_preview=False)
        elif choice == "4":
            if puller.connect():
                screenshot_path = puller.save_screenshot()
                if screenshot_path:
                    print("截图完成!")
                else:
                    print("截图失败!")
                puller.stop()
        elif choice == "5":
            test_rtmp_connection(rtmp_url)
        elif choice == "6":
            main()  # 重新开始
        else:
            print("无效选择")

    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
