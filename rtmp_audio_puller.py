import cv2
import threading
import time
import os
import subprocess
import signal
from datetime import datetime

class RTMPAudioVideoPuller:
    def __init__(self, rtmp_url, output_dir="./output", display_scale=0.5):
        self.rtmp_url = rtmp_url
        self.output_dir = output_dir
        self.display_scale = display_scale
        self.cap = None
        self.is_running = False
        self.frame_count = 0
        self.start_time = None
        self.ffplay_process = None
        
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
    
    def start_audio_playback(self):
        """启动音频播放"""
        try:
            print("启动音频播放...")
            # 使用ffplay播放音频，隐藏视频窗口
            cmd = [
                'ffplay',
                '-nodisp',  # 不显示视频
                '-autoexit',  # 流结束时自动退出
                '-loglevel', 'quiet',  # 静默模式
                self.rtmp_url
            ]
            
            self.ffplay_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            print("✓ 音频播放已启动")
            return True
            
        except FileNotFoundError:
            print("⚠️ FFplay未找到，无法播放音频")
            print("请安装FFmpeg: https://ffmpeg.org/download.html")
            return False
        except Exception as e:
            print(f"音频播放启动失败: {e}")
            return False
    
    def stop_audio_playback(self):
        """停止音频播放"""
        if self.ffplay_process:
            try:
                self.ffplay_process.terminate()
                self.ffplay_process.wait(timeout=3)
                print("音频播放已停止")
            except subprocess.TimeoutExpired:
                self.ffplay_process.kill()
                print("强制停止音频播放")
            except Exception as e:
                print(f"停止音频播放时出错: {e}")
            finally:
                self.ffplay_process = None
    
    def connect(self):
        """连接到RTMP流"""
        print(f"正在连接到: {self.rtmp_url}")
        print("提示: 如果连接失败，请检查:")
        print("  1. RTMP服务器是否运行")
        print("  2. 流地址是否正确")
        print("  3. 防火墙设置")
        
        # 设置连接参数
        self.cap = cv2.VideoCapture()
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        # 尝试打开流
        success = self.cap.open(self.rtmp_url)
        
        if not success or not self.cap.isOpened():
            print("错误: 无法连接到RTMP流")
            return False
        
        # 尝试读取第一帧来验证连接
        print("验证流连接...")
        ret, frame = self.cap.read()
        if not ret:
            print("错误: 无法读取流数据")
            return False
        
        # 获取流信息
        fps = self.cap.get(cv2.CAP_PROP_FPS)
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"连接成功!")
        print(f"分辨率: {width}x{height}")
        print(f"帧率: {fps} FPS")
        
        return True
    
    def start_pulling(self, save_video=False, show_preview=True, play_audio=True):
        """开始拉流"""
        if not self.connect():
            return
        
        self.is_running = True
        self.start_time = time.time()
        
        # 启动音频播放
        audio_started = False
        if play_audio:
            audio_started = self.start_audio_playback()
        
        # 如果需要保存视频，设置视频写入器
        video_writer = None
        if save_video:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = os.path.join(self.output_dir, f"stream_{timestamp}.mp4")
            
            fps = self.cap.get(cv2.CAP_PROP_FPS) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            print(f"视频将保存到: {output_path}")
        
        print("开始拉流...")
        if show_preview:
            print("控制键:")
            print("  q - 退出")
            print("  + - 放大画面")
            print("  - - 缩小画面") 
            print("  r - 重置缩放")
            print("  d - 显示调试信息")
            print("  s - 保存当前帧")
            if audio_started:
                print("  m - 重启音频")
        
        try:
            while self.is_running:
                ret, frame = self.cap.read()
                
                if not ret:
                    print("警告: 无法读取帧，可能是流中断")
                    time.sleep(0.1)
                    continue
                
                self.frame_count += 1
                
                # 保存视频帧
                if save_video and video_writer:
                    video_writer.write(frame)
                
                # 显示预览
                if show_preview:
                    # 创建显示用的帧副本
                    display_frame = frame.copy()
                    
                    # 缩放显示画面
                    if self.display_scale != 1.0:
                        height, width = display_frame.shape[:2]
                        new_width = int(width * self.display_scale)
                        new_height = int(height * self.display_scale)
                        display_frame = cv2.resize(display_frame, (new_width, new_height))
                    
                    # 添加信息文本
                    elapsed_time = time.time() - self.start_time
                    height, width = frame.shape[:2]
                    info_text = f"Frame: {self.frame_count} | Time: {elapsed_time:.1f}s | Scale: {self.display_scale:.1f}"
                    size_text = f"Original: {width}x{height} | Display: {display_frame.shape[1]}x{display_frame.shape[0]}"
                    audio_text = f"Audio: {'ON' if audio_started else 'OFF'}"
                    
                    cv2.putText(display_frame, info_text, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 
                               0.6, (0, 255, 0), 2)
                    cv2.putText(display_frame, size_text, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 
                               0.5, (0, 255, 0), 1)
                    cv2.putText(display_frame, audio_text, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 
                               0.5, (0, 255, 0), 1)
                    
                    cv2.imshow('RTMP Stream (Video + Audio)', display_frame)
                    
                    # 检查按键
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        break
                    elif key == ord('+') or key == ord('='):  # 放大
                        self.display_scale = min(2.0, self.display_scale + 0.1)
                        print(f"缩放比例: {self.display_scale:.1f}")
                    elif key == ord('-'):  # 缩小
                        self.display_scale = max(0.1, self.display_scale - 0.1)
                        print(f"缩放比例: {self.display_scale:.1f}")
                    elif key == ord('r'):  # 重置
                        self.display_scale = 1.0
                        print("重置缩放比例: 1.0")
                    elif key == ord('d'):  # 调试信息
                        print(f"画面调试信息:")
                        print(f"  原始尺寸: {frame.shape}")
                        print(f"  像素范围: {frame.min()} - {frame.max()}")
                        print(f"  平均颜色: {frame.mean(axis=(0,1))}")
                        print(f"  音频状态: {'播放中' if self.ffplay_process else '未播放'}")
                    elif key == ord('s'):  # 保存当前帧
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        debug_path = os.path.join(self.output_dir, f"debug_frame_{timestamp}.jpg")
                        cv2.imwrite(debug_path, frame)
                        print(f"调试帧已保存: {debug_path}")
                    elif key == ord('m') and play_audio:  # 重启音频
                        print("重启音频...")
                        self.stop_audio_playback()
                        time.sleep(0.5)
                        audio_started = self.start_audio_playback()
                
                # 打印统计信息
                if self.frame_count % 100 == 0:
                    elapsed_time = time.time() - self.start_time
                    fps_actual = self.frame_count / elapsed_time if elapsed_time > 0 else 0
                    print(f"已处理 {self.frame_count} 帧, 实际FPS: {fps_actual:.2f}")
        
        except KeyboardInterrupt:
            print("\n用户中断拉流")
        
        finally:
            self.stop()
            if video_writer:
                video_writer.release()
            if show_preview:
                cv2.destroyAllWindows()
    
    def stop(self):
        """停止拉流"""
        self.is_running = False
        self.stop_audio_playback()
        if self.cap:
            self.cap.release()
        print("拉流已停止")

def main():
    # RTMP流地址
    rtmp_url = "rtmp://127.0.0.1:1935/live/stream"
    
    print("RTMP音视频拉流工具")
    print("=" * 50)
    print("1. 视频预览 + 音频播放")
    print("2. 视频预览 + 音频播放 + 保存视频")
    print("3. 仅音频播放 (无视频)")
    print("4. 仅视频预览 (无音频)")
    print("5. 自定义RTMP地址")
    
    try:
        choice = input("请选择模式 (1-5): ").strip()
        
        if choice == "5":
            rtmp_url = input("请输入RTMP地址: ").strip()
            if not rtmp_url:
                print("地址不能为空")
                return
            main()  # 重新选择模式
            return
        
        # 设置显示缩放比例
        display_scale = 0.7  # 默认缩放到70%
        if choice in ["1", "2", "4"]:  # 需要显示预览的模式
            scale_input = input("显示缩放比例 (0.1-2.0, 默认0.7): ").strip()
            if scale_input:
                try:
                    display_scale = float(scale_input)
                    display_scale = max(0.1, min(2.0, display_scale))
                except ValueError:
                    print("无效输入，使用默认缩放0.7")
                    display_scale = 0.7
        
        # 创建拉流器
        puller = RTMPAudioVideoPuller(rtmp_url, display_scale=display_scale)
        
        if choice == "1":
            puller.start_pulling(save_video=False, show_preview=True, play_audio=True)
        elif choice == "2":
            puller.start_pulling(save_video=True, show_preview=True, play_audio=True)
        elif choice == "3":
            # 仅音频模式
            if puller.start_audio_playback():
                print("音频播放中... 按 Ctrl+C 停止")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n停止音频播放")
                finally:
                    puller.stop_audio_playback()
        elif choice == "4":
            puller.start_pulling(save_video=False, show_preview=True, play_audio=False)
        else:
            print("无效选择")
    
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
