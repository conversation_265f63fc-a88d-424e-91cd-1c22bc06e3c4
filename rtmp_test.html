<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTMP流播放测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .player-section {
            margin-bottom: 30px;
        }
        
        .player-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #444;
        }
        
        video {
            width: 100%;
            max-width: 800px;
            height: auto;
            background: #000;
            border-radius: 5px;
        }
        
        .controls {
            margin: 15px 0;
        }
        
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        button {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .info-box {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
        
        .tech-info {
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 RTMP流播放测试工具</h1>
        
        <div class="warning">
            <strong>⚠️ 重要提示：</strong><br>
            现代浏览器不直接支持RTMP协议。RTMP流需要转换为HLS或WebRTC等格式才能在浏览器中播放。
            这个页面主要用于测试转换后的流或其他支持的格式。
        </div>
        
        <!-- HLS播放器 -->
        <div class="player-section">
            <div class="player-title">📺 HLS播放器 (推荐)</div>
            <div class="controls">
                <input type="text" id="hlsUrl" placeholder="输入HLS地址 (http://.../*.m3u8)" 
                       value="http://127.0.0.1:8080/live/stream.m3u8">
                <button onclick="playHLS()">播放HLS</button>
                <button onclick="stopHLS()">停止</button>
            </div>
            <video id="hlsPlayer" controls muted>
                您的浏览器不支持视频播放
            </video>
            <div id="hlsStatus" class="status" style="display:none;"></div>
        </div>
        
        <!-- WebRTC播放器 -->
        <div class="player-section">
            <div class="player-title">🌐 WebRTC播放器</div>
            <div class="controls">
                <input type="text" id="webrtcUrl" placeholder="输入WebRTC地址" 
                       value="ws://127.0.0.1:8080/live/stream">
                <button onclick="playWebRTC()">播放WebRTC</button>
                <button onclick="stopWebRTC()">停止</button>
            </div>
            <video id="webrtcPlayer" controls muted autoplay>
                您的浏览器不支持视频播放
            </video>
            <div id="webrtcStatus" class="status" style="display:none;"></div>
        </div>
        
        <!-- HTTP-FLV播放器 -->
        <div class="player-section">
            <div class="player-title">📡 HTTP-FLV播放器</div>
            <div class="controls">
                <input type="text" id="flvUrl" placeholder="输入FLV地址" 
                       value="http://127.0.0.1:8080/live/stream.flv">
                <button onclick="playFLV()">播放FLV</button>
                <button onclick="stopFLV()">停止</button>
            </div>
            <video id="flvPlayer" controls muted>
                您的浏览器不支持视频播放
            </video>
            <div id="flvStatus" class="status" style="display:none;"></div>
        </div>
        
        <div class="info-box">
            <h3>💡 使用说明：</h3>
            <ul>
                <li><strong>HLS (.m3u8)</strong>: 最兼容的格式，延迟较高但稳定</li>
                <li><strong>WebRTC</strong>: 低延迟，需要特殊服务器支持</li>
                <li><strong>HTTP-FLV</strong>: 中等延迟，需要flv.js库支持</li>
            </ul>
            
            <h3>🔧 RTMP转换方案：</h3>
            <p>要在浏览器中播放RTMP流，你需要：</p>
            <ol>
                <li><strong>使用FFmpeg转换：</strong><br>
                    <code>ffmpeg -i rtmp://127.0.0.1:1935/live/stream -c copy -f hls -hls_time 2 -hls_list_size 3 -hls_flags delete_segments output.m3u8</code>
                </li>
                <li><strong>使用SRS服务器：</strong> 支持RTMP输入，HLS/WebRTC输出</li>
                <li><strong>使用Nginx-RTMP：</strong> 配置HLS输出</li>
            </ol>
        </div>
        
        <div id="techInfo" class="tech-info">
            <strong>技术信息：</strong><br>
            浏览器: <span id="browserInfo"></span><br>
            支持的视频格式: <span id="videoSupport"></span><br>
            当前时间: <span id="currentTime"></span>
        </div>
    </div>

    <!-- HLS.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    
    <script>
        // 更新技术信息
        function updateTechInfo() {
            document.getElementById('browserInfo').textContent = navigator.userAgent;
            
            const video = document.createElement('video');
            const formats = [];
            if (video.canPlayType('video/mp4')) formats.push('MP4');
            if (video.canPlayType('video/webm')) formats.push('WebM');
            if (video.canPlayType('video/ogg')) formats.push('OGG');
            document.getElementById('videoSupport').textContent = formats.join(', ');
            
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }
        
        // HLS播放器
        let hlsInstance = null;
        
        function playHLS() {
            const video = document.getElementById('hlsPlayer');
            const url = document.getElementById('hlsUrl').value;
            const status = document.getElementById('hlsStatus');
            
            if (!url) {
                showStatus('hlsStatus', 'error', '请输入HLS地址');
                return;
            }
            
            if (Hls.isSupported()) {
                if (hlsInstance) {
                    hlsInstance.destroy();
                }
                
                hlsInstance = new Hls({
                    debug: false,
                    enableWorker: true,
                    lowLatencyMode: true,
                });
                
                hlsInstance.loadSource(url);
                hlsInstance.attachMedia(video);
                
                hlsInstance.on(Hls.Events.MANIFEST_PARSED, function() {
                    showStatus('hlsStatus', 'success', 'HLS流加载成功，开始播放...');
                    video.play().catch(e => {
                        showStatus('hlsStatus', 'error', '播放失败: ' + e.message);
                    });
                });
                
                hlsInstance.on(Hls.Events.ERROR, function(event, data) {
                    showStatus('hlsStatus', 'error', 'HLS错误: ' + data.details);
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生支持
                video.src = url;
                video.play().then(() => {
                    showStatus('hlsStatus', 'success', '使用原生HLS播放');
                }).catch(e => {
                    showStatus('hlsStatus', 'error', '播放失败: ' + e.message);
                });
            } else {
                showStatus('hlsStatus', 'error', '浏览器不支持HLS播放');
            }
        }
        
        function stopHLS() {
            const video = document.getElementById('hlsPlayer');
            video.pause();
            video.src = '';
            if (hlsInstance) {
                hlsInstance.destroy();
                hlsInstance = null;
            }
            showStatus('hlsStatus', 'info', 'HLS播放已停止');
        }
        
        // WebRTC播放器 (简化版)
        function playWebRTC() {
            showStatus('webrtcStatus', 'info', 'WebRTC需要专门的库支持，这里仅作演示');
        }
        
        function stopWebRTC() {
            showStatus('webrtcStatus', 'info', 'WebRTC播放已停止');
        }
        
        // FLV播放器 (需要flv.js)
        function playFLV() {
            showStatus('flvStatus', 'info', 'HTTP-FLV需要flv.js库支持，这里仅作演示');
        }
        
        function stopFLV() {
            showStatus('flvStatus', 'info', 'FLV播放已停止');
        }
        
        // 显示状态信息
        function showStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
            element.style.display = 'block';
            
            // 3秒后隐藏成功和信息状态
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    element.style.display = 'none';
                }, 3000);
            }
        }
        
        // 页面加载时更新信息
        window.onload = function() {
            updateTechInfo();
            setInterval(updateTechInfo, 30000); // 每30秒更新一次时间
        };
    </script>
</body>
</html>
