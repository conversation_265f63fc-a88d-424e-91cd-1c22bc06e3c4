#!/usr/bin/env python3
"""
RTMP连接测试工具
用于诊断RTMP流连接问题
"""

import cv2
import socket
import subprocess
import sys

def test_network_connection(host, port):
    """测试网络连接"""
    print(f"测试网络连接: {host}:{port}")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✓ 网络连接正常 ({host}:{port})")
            return True
        else:
            print(f"✗ 网络连接失败 ({host}:{port})")
            return False
    except Exception as e:
        print(f"✗ 网络测试异常: {e}")
        return False

def test_opencv_rtmp(rtmp_url):
    """测试OpenCV RTMP支持"""
    print(f"测试OpenCV RTMP: {rtmp_url}")
    
    try:
        cap = cv2.VideoCapture()
        
        # 设置一些参数
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        cap.set(cv2.CAP_PROP_FPS, 25)
        
        print("尝试打开流...")
        success = cap.open(rtmp_url)
        
        if not success:
            print("✗ OpenCV无法打开RTMP流")
            cap.release()
            return False
        
        print("✓ OpenCV成功打开流")
        
        # 尝试读取帧
        print("尝试读取帧...")
        ret, frame = cap.read()
        
        if ret and frame is not None:
            print(f"✓ 成功读取帧，尺寸: {frame.shape}")
            cap.release()
            return True
        else:
            print("✗ 无法读取帧数据")
            cap.release()
            return False
            
    except Exception as e:
        print(f"✗ OpenCV测试异常: {e}")
        return False

def test_ffmpeg_support():
    """测试FFmpeg支持"""
    print("测试FFmpeg支持...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ FFmpeg可用")
            return True
        else:
            print("✗ FFmpeg不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("✗ FFmpeg未安装")
        return False

def test_with_ffplay(rtmp_url):
    """使用ffplay测试流"""
    print(f"使用ffplay测试: {rtmp_url}")
    try:
        cmd = ['ffplay', '-v', 'quiet', '-autoexit', '-t', '3', rtmp_url]
        result = subprocess.run(cmd, timeout=10)
        if result.returncode == 0:
            print("✓ ffplay可以播放流")
            return True
        else:
            print("✗ ffplay无法播放流")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠ ffplay不可用")
        return False

def main():
    print("RTMP连接诊断工具")
    print("=" * 50)
    
    # 默认测试地址
    rtmp_urls = [
        "rtmp://127.0.0.1/live/stream",
        "rtmp://127.0.0.1:1935/live/stream",
    ]
    
    # 用户输入
    custom_url = input("输入自定义RTMP地址 (回车使用默认): ").strip()
    if custom_url:
        rtmp_urls = [custom_url]
    
    for rtmp_url in rtmp_urls:
        print(f"\n测试地址: {rtmp_url}")
        print("-" * 40)
        
        # 解析URL
        if rtmp_url.startswith("rtmp://"):
            url_part = rtmp_url[7:]  # 移除 rtmp://
            if ":" in url_part:
                host = url_part.split(":")[0]
                port_path = url_part.split(":", 1)[1]
                if "/" in port_path:
                    port = int(port_path.split("/")[0])
                else:
                    port = int(port_path)
            else:
                host = url_part.split("/")[0]
                port = 1935  # RTMP默认端口
        else:
            print("✗ 无效的RTMP URL格式")
            continue
        
        # 1. 测试网络连接
        network_ok = test_network_connection(host, port)
        
        # 2. 测试FFmpeg支持
        ffmpeg_ok = test_ffmpeg_support()
        
        # 3. 如果有ffplay，测试流
        if ffmpeg_ok:
            test_with_ffplay(rtmp_url)
        
        # 4. 测试OpenCV
        opencv_ok = test_opencv_rtmp(rtmp_url)
        
        print(f"\n总结:")
        print(f"网络连接: {'✓' if network_ok else '✗'}")
        print(f"FFmpeg: {'✓' if ffmpeg_ok else '✗'}")
        print(f"OpenCV: {'✓' if opencv_ok else '✗'}")
        
        if opencv_ok:
            print("🎉 RTMP流测试成功！可以使用rtmp_puller.py")
            break
        else:
            print("❌ RTMP流测试失败")
    
    print("\n建议:")
    print("1. 确保RTMP服务器正在运行")
    print("2. 检查流地址是否正确")
    print("3. 尝试使用VLC播放器测试流")
    print("4. 检查防火墙设置")

if __name__ == "__main__":
    main()
