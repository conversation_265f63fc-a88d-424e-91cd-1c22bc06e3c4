import os,websocket,json,datetime
import queue

config ={
          "url":"127.0.0.1",
}

text_queue = queue.Queue()
player = None

def on_message(ws, message):
          timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
          print(f"[{timestamp}] Received: {message}")


def on_error(ws, error):
          print(f"[error] {error}")

def on_close(ws,close_status_code, close_msg):
          print("[close] Connection closed")


def on_open(ws):
          print("[连接成功] WebSocket连接已建立")
          message = {
          "Action": "TextCloneAudioSpeak",
          "Data": {
                  "Text": "奶白复古外观颜值在线,除了烧开水还有四档水温调节"
              }
          }

          ws.send(json.dumps(message))
          print("Sent: " + json.dumps(message))

ws_url = f"ws://{config['url']}:8765"

ws = websocket.WebSocketApp(ws_url, on_message = on_message, on_error = on_error, on_close = on_close, on_open = on_open)

try:
    print(f"尝试连接到: {ws_url}")
    ws.run_forever(ping_interval=60, ping_timeout=10)
except KeyboardInterrupt:
    print("用户中断连接")
except Exception as e:
    print(f"连接错误: {e}")