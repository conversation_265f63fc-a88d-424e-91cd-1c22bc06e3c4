import os,websocket,json,datetime
import queue

config ={
          "url":"127.0.0.1",
}

text_queue = queue.Queue()
player = None

def on_message(ws, message):
          timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
          print(f"[{timestamp}] Received: {message}")


def on_error(ws, error):
          print(f"[error] {error}")

def on_close(ws,close_status_code, close_msg):
          print("[close] Connection closed")


def on_open(ws):
          message = {
                  "Action": "Question",
                    "Data": {
                              "Prompt": "吃饭了么"
                    }
          }


          ws.send(json.dumps(message))
          print("Sent: " + json.dumps(message))

ws_url = f"ws://{config['url']}:8765"

ws = websocket.WebSocketApp(ws_url, on_message = on_message, on_error = on_error, on_close = on_close)

ws.run_forever()