const WebSocket = require('ws');

const config = {
    url: "127.0.0.1",
    port: 8765
};

// 创建 WebSocket 连接
const wsUrl = `ws://${config.url}:${config.port}`;
console.log(`尝试连接到: ${wsUrl}`);

const ws = new WebSocket(wsUrl);

// 连接打开时的处理
ws.on('open', function open() {
    console.log('[连接成功] WebSocket连接已建立');
    
    const message = {
        "Action": "TextCloneAudioSpeak",
        "Data": {
            "Text": "奶白复古外观颜值在线,除了烧开水还有四档水温调节"
        }
    };
    
    ws.send(JSON.stringify(message));
    console.log('Sent: ' + JSON.stringify(message));
});

// 接收消息时的处理
ws.on('message', function message(data) {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 23);
    console.log(`[${timestamp}] Received: ${data}`);
});

// 错误处理
ws.on('error', function error(err) {
    console.log(`[error] ${err}`);
});

// 连接关闭时的处理
ws.on('close', function close(code, reason) {
    console.log(`[close] Connection closed. Code: ${code}, Reason: ${reason}`);
});

// 处理程序中断
process.on('SIGINT', function() {
    console.log('\n用户中断连接');
    ws.close();
    process.exit(0);
});
